// 插件加载时在控制台输出信息
console.log('SteamUI助手后台脚本已加载');

// 监听来自content script的消息
chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
  console.log('后台收到消息:', message);
  
  if (message.action === "saveGameInfo") {
    // 保存游戏信息
    saveGameInfo(message.gameInfo);
    sendResponse({success: true, message: "游戏信息已保存"});
  } else if (message.action === "getGameList") {
    // 获取已保存的游戏列表
    getGameList(sendResponse);
    return true; // 异步返回结果需要返回true
  } else if (message.action === "clearGameList") {
    // 清空游戏列表
    clearGameList(sendResponse);
    return true;
  } else if (message.action === "removeGame") {
    // 移除特定游戏
    removeGame(message.appId, sendResponse);
    return true;
  }
});

// 保存游戏信息到存储
function saveGameInfo(gameInfo) {
  console.log('保存游戏信息:', gameInfo);
  
  chrome.storage.local.get(['gameList'], function(result) {
    let gameList = result.gameList || [];
    console.log('当前游戏列表:', gameList);
    
    // 检查是否已经存在相同的APPID
    const existingIndex = gameList.findIndex(game => game.appId === gameInfo.appId);
    
    if (existingIndex !== -1) {
      // 如果已存在，更新时间戳
      console.log('更新已存在的游戏信息:', gameInfo.appId);
      gameList[existingIndex].timestamp = gameInfo.timestamp;
    } else {
      // 如果不存在，添加到列表
      console.log('添加新游戏信息:', gameInfo);
      gameList.push(gameInfo);
    }
    
    // 保存更新后的列表
    chrome.storage.local.set({gameList: gameList}, function() {
      console.log('游戏列表已更新，当前共有', gameList.length, '个游戏');
    });
  });
}

// 获取已保存的游戏列表
function getGameList(sendResponse) {
  console.log('获取游戏列表');
  
  chrome.storage.local.get(['gameList'], function(result) {
    const gameList = result.gameList || [];
    console.log('返回游戏列表，共', gameList.length, '个游戏');
    sendResponse({gameList: gameList});
  });
}

// 清空游戏列表
function clearGameList(sendResponse) {
  console.log('清空游戏列表');
  
  chrome.storage.local.set({gameList: []}, function() {
    console.log('游戏列表已清空');
    sendResponse({success: true});
  });
}

// 移除特定游戏
function removeGame(appId, sendResponse) {
  console.log('移除游戏:', appId);
  
  chrome.storage.local.get(['gameList'], function(result) {
    let gameList = result.gameList || [];
    const newGameList = gameList.filter(game => game.appId !== appId);
    
    chrome.storage.local.set({gameList: newGameList}, function() {
      console.log('游戏已移除，剩余', newGameList.length, '个游戏');
      sendResponse({success: true, gameList: newGameList});
    });
  });
}

// 添加插件安装和更新事件监听
chrome.runtime.onInstalled.addListener(function(details) {
  if (details.reason === "install") {
    console.log("SteamUI助手已安装");
  } else if (details.reason === "update") {
    console.log("SteamUI助手已更新");
  }
}); 