# SteamUI助手

这是一个专门为 [steamui.com](https://steamui.com/) 网站设计的浏览器扩展，用于记录您点击复制的游戏 APPID 和对应的游戏名称。

## 功能

- 自动监控并记录您在 steamui.com 网站上点击复制的游戏 APPID
- 同时记录对应的游戏名称
- 在弹出窗口中查看已记录的游戏列表
- 支持导出记录到 TXT 文件（格式：APPID----游戏名称）
- 支持删除单个记录或清空所有记录

## 使用方法

1. 在 Chrome 浏览器中安装此扩展
2. 访问 [steamui.com](https://steamui.com/) 网站
3. 当您点击游戏卡片中的 APPID 进行复制时，插件会自动记录该游戏的 APPID 和名称
4. 点击浏览器工具栏中的插件图标，可以查看已记录的游戏列表
5. 在弹出窗口中，您可以：
   - 查看已记录的游戏列表
   - 点击"导出到TXT"按钮导出记录
   - 点击"清空列表"按钮清空所有记录
   - 点击游戏条目右侧的"×"按钮删除单个记录

## 安装方法

### 从 Chrome 网上应用店安装（推荐）

1. 访问 Chrome 网上应用店
2. 搜索"SteamUI助手"
3. 点击"添加到 Chrome"按钮

### 手动安装（开发者模式）

1. 下载此仓库的 ZIP 文件并解压
2. 打开 Chrome 浏览器，输入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择解压后的文件夹

## 注意事项

- 此插件仅适用于 [steamui.com](https://steamui.com/) 网站
- 记录的游戏信息存储在浏览器本地，不会上传到任何服务器
- 如果网站结构发生变化，插件可能需要更新以适应新的结构

## 隐私政策

此扩展不会收集或上传任何个人数据。所有记录的游戏信息仅存储在您的浏览器本地存储中，您可以随时清除这些数据。 