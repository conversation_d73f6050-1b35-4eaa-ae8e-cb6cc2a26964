// DOM元素
const gameListElement = document.getElementById('gameList');
const exportBtn = document.getElementById('exportBtn');
const clearBtn = document.getElementById('clearBtn');

// 页面加载时获取游戏列表
document.addEventListener('DOMContentLoaded', function() {
  console.log('弹出窗口已加载');
  loadGameList();
  
  // 检查当前标签页是否是steamui.com
  checkCurrentTab();
});

// 检查当前标签页
function checkCurrentTab() {
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    const currentTab = tabs[0];
    console.log('当前标签页:', currentTab.url);
    
    // 检查是否是steamui.com网站
    if (currentTab.url.includes('steamui.com')) {
      console.log('当前是steamui.com网站，分析页面结构');
      
      // 添加分析按钮
      const header = document.querySelector('header');
      const analyzeBtn = document.createElement('button');
      analyzeBtn.className = 'btn btn-info';
      analyzeBtn.textContent = '分析页面结构';
      analyzeBtn.style.marginTop = '10px';
      analyzeBtn.style.width = '100%';
      analyzeBtn.style.backgroundColor = '#3498db';
      
      analyzeBtn.addEventListener('click', function() {
        analyzePageStructure(currentTab.id);
      });
      
      header.appendChild(analyzeBtn);
    }
  });
}

// 分析页面结构
function analyzePageStructure(tabId) {
  console.log('发送分析页面结构请求到标签页:', tabId);
  
  chrome.tabs.sendMessage(tabId, {action: "analyzePageStructure"}, function(response) {
    if (chrome.runtime.lastError) {
      console.error('发送消息错误:', chrome.runtime.lastError);
      showAnalysisResult({
        error: true,
        message: '无法与页面通信，请刷新页面后重试'
      });
      return;
    }
    
    console.log('页面结构分析结果:', response);
    showAnalysisResult(response);
  });
}

// 显示分析结果
function showAnalysisResult(result) {
  // 创建或更新分析结果元素
  let resultElement = document.getElementById('analysisResult');
  
  if (!resultElement) {
    resultElement = document.createElement('div');
    resultElement.id = 'analysisResult';
    resultElement.className = 'analysis-result';
    document.querySelector('.container').insertBefore(resultElement, document.querySelector('.actions'));
  }
  
  // 设置内容
  if (result.error) {
    resultElement.innerHTML = `
      <div class="error-message">
        <p>${result.message}</p>
      </div>
    `;
  } else {
    resultElement.innerHTML = `
      <div class="result-box">
        <h3>页面分析结果</h3>
        <ul>
          <li>APPID元素数量: ${result.appidElements || 0}</li>
          <li>游戏卡片元素数量: ${result.gameCardElements || 0}</li>
          <li>游戏标题元素数量: ${result.gameTitleElements || 0}</li>
          <li>记录按钮数量: ${result.recordButtons || 0}</li>
        </ul>
        <p class="tip">提示: 如果记录按钮数量为0，说明页面上没有可识别的APPID元素</p>
      </div>
    `;
  }
  
  // 添加样式
  const style = document.createElement('style');
  style.textContent = `
    .analysis-result {
      margin: 10px 0;
    }
    .result-box {
      background-color: #2a475e;
      border-radius: 4px;
      padding: 10px;
    }
    .result-box h3 {
      font-size: 16px;
      margin-bottom: 8px;
      color: #66c0f4;
    }
    .result-box ul {
      list-style: none;
      padding-left: 0;
      margin-bottom: 8px;
    }
    .result-box li {
      margin-bottom: 4px;
    }
    .tip {
      font-size: 12px;
      color: #8f98a0;
      margin-top: 8px;
    }
    .error-message {
      background-color: rgba(231, 76, 60, 0.2);
      border-left: 3px solid #e74c3c;
      padding: 10px;
      border-radius: 4px;
    }
  `;
  document.head.appendChild(style);
}

// 加载游戏列表
function loadGameList() {
  console.log('加载游戏列表');
  
  chrome.runtime.sendMessage({action: "getGameList"}, function(response) {
    console.log('获取到游戏列表响应:', response);
    const gameList = response.gameList || [];
    renderGameList(gameList);
  });
}

// 渲染游戏列表
function renderGameList(gameList) {
  console.log('渲染游戏列表，共', gameList.length, '个游戏');
  
  // 清空列表
  gameListElement.innerHTML = '';
  
  // 如果列表为空，显示提示信息
  if (gameList.length === 0) {
    const emptyMessage = document.createElement('div');
    emptyMessage.className = 'empty-message';
    emptyMessage.textContent = '暂无记录的游戏';
    gameListElement.appendChild(emptyMessage);
    return;
  }
  
  // 按时间戳倒序排序，最新的在前面
  gameList.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  
  // 添加游戏项
  gameList.forEach(game => {
    const gameItem = document.createElement('div');
    gameItem.className = 'game-item';
    
    const gameInfo = document.createElement('div');
    gameInfo.className = 'game-info';
    
    const gameName = document.createElement('div');
    gameName.className = 'game-name';
    gameName.textContent = game.name;
    
    const gameAppId = document.createElement('div');
    gameAppId.className = 'game-appid';
    gameAppId.textContent = `AppID: ${game.appId}`;
    
    gameInfo.appendChild(gameName);
    gameInfo.appendChild(gameAppId);
    
    const removeBtn = document.createElement('button');
    removeBtn.className = 'remove-btn';
    removeBtn.innerHTML = '×';
    removeBtn.title = '移除';
    removeBtn.dataset.appid = game.appId;
    removeBtn.addEventListener('click', function() {
      removeGame(game.appId);
    });
    
    gameItem.appendChild(gameInfo);
    gameItem.appendChild(removeBtn);
    
    gameListElement.appendChild(gameItem);
  });
}

// 移除游戏
function removeGame(appId) {
  console.log('移除游戏:', appId);
  
  chrome.runtime.sendMessage({
    action: "removeGame",
    appId: appId
  }, function(response) {
    console.log('移除游戏响应:', response);
    if (response.success) {
      renderGameList(response.gameList);
    }
  });
}

// 复制所有游戏信息到剪贴板
function copyAllGameInfo() {
  console.log('复制所有游戏信息');
  
  chrome.runtime.sendMessage({action: "getGameList"}, function(response) {
    const gameList = response.gameList || [];
    
    if (gameList.length === 0) {
      alert('没有记录的游戏信息可复制');
      return;
    }
    
    // 格式化数据为文本
    let textContent = "";
    gameList.forEach(game => {
      textContent += `${game.appId}----${game.name}\n`;
    });
    
    // 复制到剪贴板
    navigator.clipboard.writeText(textContent).then(() => {
      // 显示成功提示
      const exportBtn = document.getElementById('exportBtn');
      const originalText = exportBtn.textContent;
      exportBtn.textContent = '复制成功!';
      exportBtn.style.backgroundColor = '#2ecc71';
      
      // 3秒后恢复按钮文本
      setTimeout(() => {
        exportBtn.textContent = originalText;
        exportBtn.style.backgroundColor = '';
      }, 2000);
      
      console.log('游戏信息已复制到剪贴板');
    }).catch(err => {
      console.error('复制失败:', err);
      alert('复制失败，请重试');
    });
  });
}

// 导出游戏列表
exportBtn.addEventListener('click', function() {
  console.log('点击复制按钮');
  copyAllGameInfo();
});

// 清空游戏列表
clearBtn.addEventListener('click', function() {
  console.log('点击清空按钮');
  if (confirm('确定要清空所有记录的游戏吗？')) {
    chrome.runtime.sendMessage({action: "clearGameList"}, function(response) {
      console.log('清空游戏列表响应:', response);
      if (response.success) {
        loadGameList();
      }
    });
  }
}); 