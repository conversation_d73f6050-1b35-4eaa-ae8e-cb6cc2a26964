// 插件加载时在控制台输出信息
console.log('SteamUI助手已加载，正在监听复制操作...');
console.log('当前页面URL:', window.location.href);

// 全局变量，存储已找到的APPID元素
let knownAppIdElements = new Set();

// 在页面加载完成后执行初始化
window.addEventListener('load', function() {
  console.log('页面加载完成，SteamUI助手初始化完成');
  
  // 添加自定义样式
  addCustomStyles();
  
  // 设置MutationObserver监听DOM变化
  setupMutationObserver();
  
  // 初始扫描页面
  checkForNewAppIdElements(document.body);
});

// 添加自定义样式
function addCustomStyles() {
  const style = document.createElement('style');
  style.textContent = `
    .steamui-helper-notification {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background-color: rgba(102, 192, 244, 0.9);
      color: #fff;
      padding: 10px 15px;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      z-index: 10000;
      font-size: 14px;
      transform: translateY(30px);
      opacity: 0;
      transition: transform 0.3s, opacity 0.3s;
    }
    
    .steamui-helper-notification.show {
      transform: translateY(0);
      opacity: 1;
    }
    
    .steamui-helper-record-btn {
      cursor: pointer;
      margin-left: 8px;
      color: #ff4500;
      font-size: 18px;
      font-weight: bold;
      background-color: #f0f0f0;
      border-radius: 4px;
      padding: 2px 6px;
      transition: all 0.2s ease;
      display: inline-block;
      box-shadow: 0 1px 3px rgba(0,0,0,0.2);
      border: 1px solid #ddd;
    }
    
    .steamui-helper-record-btn:hover {
      color: #ffffff !important;
      background-color: #ff4500;
      transform: scale(1.1);
    }
  `;
  document.head.appendChild(style);
  console.log('添加了自定义样式');
}

// 设置MutationObserver监听DOM变化
function setupMutationObserver() {
  console.log('设置MutationObserver监听DOM变化');
  
  // 创建一个观察器实例
  const observer = new MutationObserver(function(mutations) {
    let hasNewElements = false;
    
    // 处理所有变化
    mutations.forEach(function(mutation) {
      // 如果有节点添加
      if (mutation.addedNodes && mutation.addedNodes.length > 0) {
        // 检查每个添加的节点
        mutation.addedNodes.forEach(function(node) {
          // 只处理元素节点
          if (node.nodeType === Node.ELEMENT_NODE) {
            // 查找新添加的APPID元素
            if (checkForNewAppIdElements(node)) {
              hasNewElements = true;
            }
          }
        });
      }
    });
    
    if (hasNewElements) {
      console.log('检测到DOM变化，添加了新的记录按钮');
    }
  });
  
  // 配置观察选项
  const config = { 
    childList: true,  // 观察目标子节点的变化
    subtree: true     // 观察所有后代节点的变化
  };
  
  // 开始观察整个文档
  observer.observe(document.body, config);
  
  // 定期扫描页面，以防万一
  setInterval(function() {
    checkForNewAppIdElements(document.body);
  }, 2000); // 每2秒扫描一次
}

// 检查新的APPID元素
function checkForNewAppIdElements(rootNode) {
  // 查找所有可能的APPID元素
  const appidElements = rootNode.querySelectorAll('.game-appid, [data-tooltip="点击复制 APPID"]');
  let found = false;
  
  // 检查是否有新的元素
  appidElements.forEach(function(elem) {
    if (!knownAppIdElements.has(elem)) {
      console.log('找到新的APPID元素:', elem);
      knownAppIdElements.add(elem);
      
      // 为新元素添加记录按钮
      addRecordButtonToAppIdElement(elem);
      
      found = true;
    }
  });
  
  // 查找带有copyToClipboard的元素
  const elementsWithOnclick = rootNode.querySelectorAll('*[onclick]');
  elementsWithOnclick.forEach(function(elem) {
    if (elem.getAttribute('onclick') && 
        elem.getAttribute('onclick').includes('copyToClipboard') && 
        !knownAppIdElements.has(elem)) {
      console.log('找到新的copyToClipboard元素:', elem);
      knownAppIdElements.add(elem);
      
      // 为新元素添加记录按钮
      addRecordButtonToAppIdElement(elem);
      
      found = true;
    }
  });
  
  return found;
}

// 为APPID元素添加记录按钮
function addRecordButtonToAppIdElement(appIdElement) {
  // 检查元素是否已经有记录按钮
  if (appIdElement.nextSibling && appIdElement.nextSibling.classList && 
      appIdElement.nextSibling.classList.contains('steamui-helper-record-btn')) {
    return; // 已经添加过按钮
  }
  
  // 提取APPID
  const appId = extractAppId(appIdElement);
  if (!appId) return;
  
  // 创建记录按钮
  const recordBtn = document.createElement('span');
  recordBtn.className = 'steamui-helper-record-btn';
  recordBtn.innerHTML = '➕'; // 使用加号图标
  recordBtn.title = '记录此游戏';
  
  // 添加点击事件
  recordBtn.addEventListener('click', function(event) {
    event.stopPropagation(); // 阻止事件冒泡
    
    // 查找游戏名称
    const gameName = findGameName(appIdElement);
    
    // 保存游戏信息
    saveGameInfo(appId, gameName);
  });
  
  // 将按钮添加到APPID元素后面
  appIdElement.parentNode.insertBefore(recordBtn, appIdElement.nextSibling);
  
  console.log('为APPID元素添加了记录按钮:', appId);
}

// 从元素中提取APPID
function extractAppId(element) {
  // 直接从文本内容获取
  let appId = element.textContent.trim();
  
  // 如果文本内容不是纯数字，尝试从onclick属性中提取
  if (!/^\d+$/.test(appId) && element.getAttribute('onclick')) {
    const onclickAttr = element.getAttribute('onclick');
    const match = onclickAttr.match(/copyToClipboard\(this,\s*(\d+)\)/);
    if (match && match[1]) {
      appId = match[1];
    }
  }
  
  // 验证是否是有效的APPID (纯数字)
  if (/^\d+$/.test(appId)) {
    return appId;
  }
  
  return null;
}

// 查找游戏名称
function findGameName(element) {
  let gameName = "";
  
  // 方法1: 查找最近的游戏卡片
  let gameCard = element.closest('.game-card');
  if (gameCard) {
    const titleElement = gameCard.querySelector('.game-title');
    if (titleElement) {
      gameName = titleElement.textContent.trim();
      console.log('从game-title获取到游戏名称:', gameName);
      return gameName;
    }
  }
  
  // 方法2: 向上查找可能的标题元素
  let parentElement = element.parentElement;
  for (let i = 0; i < 5 && parentElement; i++) {
    // 查找可能的标题元素
    const titleElements = parentElement.querySelectorAll('h1, h2, h3, h4, .title, [class*="title"], [class*="name"]');
    for (const elem of titleElements) {
      if (elem.textContent && elem.textContent.trim()) {
        gameName = elem.textContent.trim();
        console.log('从父元素标题获取到游戏名称:', gameName);
        return gameName;
      }
    }
    
    // 如果没找到标题元素，尝试查找带有游戏名称的元素
    const possibleElements = parentElement.querySelectorAll('div, span, p');
    for (const elem of possibleElements) {
      // 排除APPID元素本身
      if (elem !== element && 
          elem.textContent && elem.textContent.trim() && 
          !/^\d+$/.test(elem.textContent.trim())) {
        gameName = elem.textContent.trim();
        // 如果文本太长，可能不是游戏名称
        if (gameName.length < 100) {
          console.log('从父元素内容获取到可能的游戏名称:', gameName);
          return gameName;
        }
      }
    }
    
    parentElement = parentElement.parentElement;
  }
  
  // 方法3: 查找页面标题
  const pageTitle = document.title;
  if (pageTitle && pageTitle !== "Steam游戏库") {
    const cleanTitle = pageTitle.replace(" - Steam游戏库", "").trim();
    if (cleanTitle) {
      console.log('从页面标题获取到可能的游戏名称:', cleanTitle);
      return cleanTitle;
    }
  }
  
  // 如果找不到游戏名称，返回未知游戏
  console.log('无法获取游戏名称，使用默认值');
  return "未知游戏";
}

// 保存游戏信息
function saveGameInfo(appId, gameName) {
  // 构建要保存的游戏信息对象
  const gameInfo = {
    appId: appId,
    name: gameName,
    timestamp: new Date().toISOString()
  };
  
  console.log('准备保存游戏信息:', gameInfo);
  
  // 发送消息到background.js保存数据
  chrome.runtime.sendMessage({
    action: "saveGameInfo",
    gameInfo: gameInfo
  }, function(response) {
    console.log('保存游戏信息响应:', response);
  });
  
  // 显示提示信息
  showNotification(`已记录: ${gameName} (${appId})`);
}

// 创建并显示通知
function showNotification(message) {
  console.log('显示通知:', message);
  
  // 创建通知元素
  const notification = document.createElement('div');
  notification.className = 'steamui-helper-notification';
  notification.textContent = message;
  
  // 添加到页面
  document.body.appendChild(notification);
  
  // 显示通知
  setTimeout(() => {
    notification.classList.add('show');
  }, 10);
  
  // 3秒后移除通知
  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 3000);
}

// 监听来自popup或background的消息
chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
  console.log('收到消息:', message);
  
  if (message.action === "getPageInfo") {
    // 获取当前页面URL
    const response = {
      url: window.location.href,
      title: document.title
    };
    console.log('发送页面信息响应:', response);
    sendResponse(response);
  } else if (message.action === "analyzePageStructure") {
    // 分析页面结构并返回
    const appidElements = document.querySelectorAll('.game-appid, [data-tooltip="点击复制 APPID"]');
    
    // 查找带有copyToClipboard的元素
    const clipboardElements = [];
    const allElements = document.querySelectorAll('*[onclick]');
    for (const elem of allElements) {
      if (elem.getAttribute('onclick') && elem.getAttribute('onclick').includes('copyToClipboard')) {
        clipboardElements.push(elem);
      }
    }
    
    const structure = {
      url: window.location.href,
      appidElements: appidElements.length,
      clipboardElements: clipboardElements.length,
      gameCardElements: document.querySelectorAll('.game-card').length,
      gameTitleElements: document.querySelectorAll('.game-title').length,
      knownElements: knownAppIdElements.size,
      recordButtons: document.querySelectorAll('.steamui-helper-record-btn').length
    };
    console.log('页面结构分析结果:', structure);
    sendResponse(structure);
  }
}); 