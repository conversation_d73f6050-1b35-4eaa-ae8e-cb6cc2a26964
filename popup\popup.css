* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', sans-serif;
  background-color: #171a21;
  color: #ffffff;
  width: 350px;
  max-height: 500px;
  overflow: hidden;
}

.container {
  padding: 16px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

header {
  margin-bottom: 16px;
  text-align: center;
}

h1 {
  font-size: 20px;
  color: #66c0f4;
  margin-bottom: 4px;
}

.subtitle {
  font-size: 14px;
  color: #c7d5e0;
}

.game-list-container {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 16px;
  max-height: 300px;
  border: 1px solid #2a475e;
  border-radius: 4px;
}

.game-list {
  padding: 8px;
}

.game-item {
  padding: 10px;
  border-bottom: 1px solid #2a475e;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.game-item:last-child {
  border-bottom: none;
}

.game-info {
  flex: 1;
}

.game-name {
  font-size: 14px;
  margin-bottom: 4px;
  color: #ffffff;
}

.game-appid {
  font-size: 12px;
  color: #8f98a0;
}

.remove-btn {
  background: none;
  border: none;
  color: #c7d5e0;
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
}

.remove-btn:hover {
  color: #ff4c4c;
}

.empty-message {
  padding: 20px;
  text-align: center;
  color: #8f98a0;
}

.actions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.btn {
  flex: 1;
  padding: 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #1b9ff1;
  color: #ffffff;
}

.btn-primary:hover {
  background-color: #66c0f4;
}

.btn-danger {
  background-color: #e74c3c;
  color: #ffffff;
}

.btn-danger:hover {
  background-color: #ff6b6b;
}

.search-actions {
  margin-bottom: 16px;
}

.btn-search {
  background-color: #ff6600;
  color: #ffffff;
  width: 100%;
}

.btn-search:hover {
  background-color: #ff8533;
}

footer {
  text-align: center;
  font-size: 12px;
  color: #8f98a0;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1b2838;
}

::-webkit-scrollbar-thumb {
  background: #2a475e;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #417a9b;
} 